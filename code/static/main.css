/* General Body Styles */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    margin: 0;
    background-color: #f8f9fa;
    color: #343a40;
    line-height: 1.6;
}

.container {
    max-width: 960px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Navbar */
.navbar {
    background-color: #ffffff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 1rem 0;
    margin-bottom: 2rem;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
    text-decoration: none;
}

.navbar-nav {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
}

.navbar-nav li {
    margin-left: 1.5rem;
}

.navbar-nav a {
    text-decoration: none;
    color: #343a40;
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav a:hover {
    color: #007bff;
}

/* Hero Section */
.hero {
    text-align: center;
    padding: 4rem 1rem;
    background-color: #e9ecef;
    border-radius: 0.3rem;
    margin-bottom: 2rem;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.hero p {
    font-size: 1.25rem;
    color: #6c757d;
}

/* Features Section */
.features {
    display: flex;
    justify-content: space-around;
    text-align: center;
    margin-bottom: 2rem;
}

.feature {
    flex: 1;
    padding: 1rem;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    border-radius: 0.25rem;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.btn-primary {
    background-color: #007bff;
    color: #ffffff;
    border: 1px solid #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
}

/* Forms */
.contact-form {
    background: #ffffff;
    padding: 2rem;
    border-radius: 0.3rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    font-size: 1rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    box-sizing: border-box;
}

/* Alerts */
.alert {
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.25rem;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

/* Footer */
.footer {
    text-align: center;
    padding: 2rem 0;
    margin-top: 2rem;
    background-color: #343a40;
    color: #ffffff;
}

/* Result Block */
.result-block {
    margin-top: 2rem;
    background-color: #e9ecef;
    padding: 1.5rem;
    border-radius: 4px;
}

.result-block h3 {
    margin-top: 0;
    margin-bottom: 1rem;
}

.result-block pre {
    background-color: #212529;
    color: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.result-value {
    font-family: monospace;
    background-color: #d1ecf1;
    padding: 2px 4px;
    border-radius: 2px;
    color: #0c5460;
}
